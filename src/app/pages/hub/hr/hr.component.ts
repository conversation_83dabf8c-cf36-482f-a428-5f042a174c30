import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { WindowService } from 'src/app/shared/services/window.service';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Table } from 'primeng/table';
import { EmailService } from 'src/app/shared/services/email.service'; // Add this import

interface UserData {
  role: string;
  adminId?: string;
  // Add other fields you expect
}
interface Branch {
  id: string;
  name: string;
}

@Component({
  selector: 'app-hr',
  templateUrl: './hr.component.html',
  styleUrls: ['./hr.component.scss'],
  providers: [MessageService, ConfirmationService],
})
export class HrComponent implements OnInit {
  @ViewChild('dt') dt!: Table;
  @ViewChild('closeInvitationCanvas')
  closeInvitationCanvas!: ElementRef<HTMLButtonElement>;

  displayAddUser: boolean = false;
  enableEditInvitation: boolean = false;

  addSuccess: boolean = false;
  rolesOptions: any[] = [
    {
      label: 'Manager',
      value: 'manager',
    },
    {
      label: 'Receptionist',
      value: 'receptionist',
    },
    {
      label: 'Technician',
      value: 'technician',
    },
    {
      label: 'Cleaner',
      value: 'cleaner',
    },
    {
      label: 'Tea Boy',
      value: 'teaboy',
    },
  ];
  // In your component class, add these properties
  branches: Branch[] = [];
  loadingBranches: boolean = true;
  inviteForm: FormGroup;
  employees: any[] = [];
  invitations: any[] = [];
  loadingEmployees: boolean = true; // Add this new property

  constructor(
    windowRef: WindowService,
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private fb: FormBuilder,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private emailService: EmailService // Add this
  ) {
    this.inviteForm = fb.group({
      token: [''],
      id: [''],
      email: ['', Validators.required],
      role: ['', Validators.required],
      managerId: [''],
      created_at: [null],
      status: ['pending'],
    });
  }

  ngOnInit() {
    this.getInvitations();
    this.getEmployees();
  }

  getInvitations() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        this.afs
          .collection('invites', (ref) =>
            ref.where('managerId', '==', user.uid)
          )
          .valueChanges()
          .subscribe((res) => {
            this.invitations = res;
          });
      }
    });
  }

  applyGlobalFilter(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    if (this.dt) {
      this.dt.filterGlobal(inputElement.value, 'contains');
    }
  }

  inviteBtnLoading: boolean = false;
  async inviteUser() {
    this.inviteBtnLoading = true;

    try {
      const user = await this.afAuth.currentUser;
      if (!user) {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'You must be logged in to send invitations',
        });
        this.inviteBtnLoading = false;
        return;
      }

      const email = this.inviteForm.value.email;

      // Check if user already exists
      const usersSnapshot = await this.afs
        .collection('users', (ref) => ref.where('email', '==', email).limit(1))
        .get()
        .toPromise();

      if (!usersSnapshot?.empty) {
        this.messageService.add({
          severity: 'warn',
          summary: 'User Exists',
          detail: 'This user already exists and cannot be invited again.',
        });
        this.inviteBtnLoading = false;
        return;
      }

      // Check for existing invitations
      const invitesSnapshot = await this.afs
        .collection('invites', (ref) =>
          ref.where('email', '==', email).limit(1)
        )
        .get()
        .toPromise();

      if (!invitesSnapshot?.empty) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Invitation Exists',
          detail: 'An invitation has already been sent to this email address.',
        });
        this.inviteBtnLoading = false;
        return;
      }
      const userDoc = await this.afs
        .collection<UserData>('users')
        .doc(user.uid)
        .get()
        .toPromise();

      if (!userDoc?.exists) {
        throw new Error('Your user profile was not found');
      }

      const userData = userDoc.data() as UserData; // Type assertion
      // Create new invitation
      const token = this.generateToken();
      const invitationId = this.afs.createId();
      const invitationData = {
        id: invitationId,
        email: email,
        role: this.inviteForm.value.role,
        token: token,
        created_at: new Date(),
        status: 'pending',
        // Use non-null assertion since we checked exists
        ...(userData!.role === 'owner'
          ? { adminId: user.uid, managerId: user.uid }
          : {
              managerId: user.uid,
              adminId: userData!.adminId || '',
            }),
      };

      await this.afs
        .collection('invites')
        .doc(invitationId)
        .set(invitationData);

      // Generate registration link (for demo purposes - in production you would send this via email)
      const registrationLink = `${window.location.origin}/create-account/${token}`;
      await this.emailService.sendInvitationEmail(
        email,
        registrationLink,
        this.inviteForm.value.role
      );
      this.messageService.add({
        severity: 'success',
        summary: 'Invitation Sent',
        detail: `User invited successfully! he will recieve an invitation email`,
      });

      this.inviteForm.reset();
    } catch (error: any) {
      console.error('Invitation error:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to send invitation: ' + error.message,
      });
    } finally {
      this.inviteBtnLoading = false;
    }
  }

  generateToken() {
    const array = new Uint8Array(30); // You can change the byte length
    window.crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join(
      ''
    );
  }

  // Update your getEmployees method
  getEmployees() {
    this.afAuth.authState.subscribe((user) => {
      if (user) {
        // Get branches where current user is the manager
        this.afs
          .collection('branches', (ref) => ref.where('uid', '==', user.uid))
          .snapshotChanges()
          .subscribe((branchChanges) => {
            this.branches = branchChanges.map((change) => ({
              id: change.payload.doc.id,
              ...(change.payload.doc.data() as any),
            }));
            this.loadingBranches = false;
          });

        // Get employees
        this.afs
          .collection('users', (ref) => ref.where('adminId', '==', user.uid))
          .snapshotChanges()
          .subscribe((changes) => {
            this.employees = [];
            changes.map((change) => {
              this.employees.push({
                id: change.payload.doc.id,
                data: change.payload.doc.data(),
              });
            });
            this.loadingEmployees = false;
          });
      }
    });
  }

  // Add this method to update branch assignment
  // Update the assignToBranch method to handle null values
  async assignToBranch(employeeId: string, branchId: string | undefined) {
    try {
      const updateData: any = {};
      const manager = this.afs
        .collection('users', (ref) =>
          ref
            .where('managerId', '==', employeeId)
            .where('role', '==', 'manager')
        )
        .snapshotChanges();
      if (branchId) {
        updateData.branchId = branchId;
        // Find and add branch name if needed
        const branch = this.branches.find((b) => b.id === branchId);
        if (branch) {
        }
      } else {
        updateData.branchId = null;
      }

      await this.afs.collection('users').doc(employeeId).update(updateData);
      if (manager) {
        await this.afs.collection('branches').doc(branchId).update({
          managerId: employeeId,
        });
      }
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Branch assignment updated',
        life: 3000,
      });
    } catch (error) {
      console.error('Error updating branch:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update branch assignment',
        life: 3000,
      });
    }
  }
  async resendEmail(invite: any) {
    try {
      await this.emailService.sendInvitationEmail(
        invite.email,
        `${window.location.origin}/create-account/${invite.token}`,
        invite.role
      );
      this.messageService.add({
        severity: 'success',
        summary: 'Invitation ReSent',
        detail: `Invitation email has been resent successfully`,
        life: 10000,
      });
    } catch (error) {
      console.error('Error resending email:', error);
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to resend invitation: ' + error,
      });
    }
  }
  getRoleSeverity(role: string): string {
    if (!role) return '';
    switch (role.toLowerCase()) {
      case 'manager':
        return 'warning';
      case 'admin':
        return 'danger';
      case 'receptionist':
        return 'info';
      case 'technician':
        return '';
      case 'cleaner':
        return 'success';
      case 'teaboy':
        return 'help';
      default:
        return '';
    }
  }
  // Add this method to check if branch is already assigned
  isBranchAssigned(branchId: string): boolean {
    if (!branchId) return false;
    return this.employees.some((emp) => emp.data.branchId === branchId);
  }

  confirmDelete(event: Event, invite: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure that you want to proceed?',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        console.log(invite);
        this.afAuth.authState.subscribe((user) => {
          if (user) {
            this.afs
              .collection('invites')
              .doc(invite.id)
              .delete()
              .then(() => {
                console.log('');
                this.messageService.add({
                  severity: 'info',
                  summary: 'Success',
                  detail: 'invitation deleted successfully!',
                });
              });
          }
        });
      },
      reject: () => {
        /*this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'You have rejected',
        });*/
      },
    });
  }

  selectedInvitation: any;
  async editInvitation(invite: any) {
    this.displayAddUser = true;
    this.enableEditInvitation = true;
    this.closeInvitationCanvas.nativeElement.click();
    this.selectedInvitation = invite;
    this.inviteForm.controls['email'].setValue(invite.email);
    this.inviteForm.controls['role'].setValue(invite.role);

    /*this.afAuth.authState.subscribe((user) => {
      if (user) {
        const token = this.generateToken();

        const registrationLink = `${window.location.origin}/create-account/${token}`;
        await this.emailService.sendInvitationEmail(
          email,
          registrationLink,
          this.inviteForm.value.role
        );
        this.messageService.add({
          severity: 'success',
          summary: 'Invitation Sent',
          detail: `User invited successfully! he will recieve an invitation email`,
          life: 10000,
        });
      }
    });*/
  }

  async editInvitedUser() {
    this.afAuth.authState.subscribe(async (user) => {
      if (user) {
        const token = this.generateToken();
        const registrationLink = `${window.location.origin}/create-account/${token}`;

        try {
          await this.emailService.sendInvitationEmail(
            this.inviteForm.value.email,
            registrationLink,
            this.inviteForm.value.role
          );

          await this.afs
            .collection('invites')
            .doc(this.selectedInvitation.id) // Make sure .id is used
            .update({
              email: this.inviteForm.value.email,
              role: this.inviteForm.value.role,
              token: token, // Update token for failed invitations
              status: 'pending', // Reset status to pending
              updatedAt: new Date(),
            });

          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Invitation updated and email sent successfully!',
          });
        } catch (error) {
          console.error('Error updating invitation or sending email:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update invitation: ' + error,
          });
        }
      }
    });
  }
}
