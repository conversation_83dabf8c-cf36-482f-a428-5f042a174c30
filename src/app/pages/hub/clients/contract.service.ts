import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import {
  Observable,
  map,
  firstValueFrom,
  from,
  tap,
  catchError,
  throwError,
  switchMap,
} from 'rxjs';
import {
  Contract,
  ContractStatus,
  Office,
  Tenant,
  TenantFromDatabase,
  PaymentTimelineEvent,
  PaymentRecord,
} from './tenant.model';
import { ChequeImageService } from './cheque-image.service';
import {
  computePaymentStatus,
  updateContractPaymentInfo,
  refreshContractPaymentStatus,
  calculateNextPaymentDate,
} from './payment.utils';
import { EmailService } from 'src/app/shared/services/email.service';

@Injectable({
  providedIn: 'root',
})
export class ContractService {
  constructor(
    private afs: AngularFirestore,
    private afAuth: AngularFireAuth,
    private chequeImageService: ChequeImageService
  ) {}

  // Get all available offices for a specific branch
  getAvailableOffices(branchId?: string): Observable<Office[]> {
    return new Observable<Office[]>((observer) => {
      this.afAuth.authState.subscribe((user) => {
        if (user) {
          let query = this.afs.collection('offices', (ref) => {
            let baseQuery = ref.where('status', '==', 'available');
            if (branchId) {
              baseQuery = baseQuery.where('branchId', '==', branchId);
            }
            return baseQuery;
          });

          query.valueChanges().subscribe((offices: any[]) => {
            const sortedOffices = offices
              .map((office) => ({
                ...office,
                officeId: office.officeId || office.id,
              }))
              .sort((a, b) => a.officeNumber - b.officeNumber);
            observer.next(sortedOffices);
          });
        } else {
          observer.next([]);
        }
      });
    });
  }

  // Get all offices (for office selection) - includes all statuses for admin view
  getAllOffices(branchId?: string): Observable<Office[]> {
    return new Observable<Office[]>((observer) => {
      this.afAuth.authState.subscribe((user) => {
        if (user) {
          let query = this.afs.collection('offices', (ref) => {
            if (branchId) {
              return ref.where('branchId', '==', branchId);
            }
            return ref;
          });

          query.valueChanges().subscribe((offices: any[]) => {
            const sortedOffices = offices
              .map((office) => ({
                ...office,
                officeId: office.officeId || office.id,
              }))
              .sort((a, b) => a.officeNumber - b.officeNumber);
            observer.next(sortedOffices);
          });
        } else {
          observer.next([]);
        }
      });
    });
  }

  // Get branches for the current user (similar to branches component)
  getUserBranches(): Observable<any[]> {
    return new Observable<any[]>((observer) => {
      this.afAuth.authState.subscribe((user) => {
        if (user) {
          // Check user type first
          this.afs
            .collection('users')
            .doc(user.uid)
            .get()
            .subscribe((userDoc) => {
              const userData = userDoc.data() as any;

              if (userData?.branchId) {
                // Regular user - get single branch
                this.afs
                  .collection('branches')
                  .doc(userData.branchId)
                  .get()
                  .subscribe((branch) => {
                    if (branch.exists) {
                      const branchData = branch.data();
                      if (branchData) {
                        observer.next([{ ...branchData, docid: branch.id }]);
                      }
                    }
                  });
              } else {
                // Admin/Manager - get all branches
                const query1 = this.afs
                  .collection('branches', (ref) =>
                    ref.where('uid', '==', user.uid)
                  )
                  .snapshotChanges();
                const query2 = this.afs
                  .collection('branches', (ref) =>
                    ref.where('managerId', '==', user.uid)
                  )
                  .snapshotChanges();

                query1.subscribe((changes1) => {
                  const branches1 = changes1.map((change) => ({
                    ...change.payload.doc.data(),
                    docid: change.payload.doc.id,
                  }));

                  query2.subscribe((changes2) => {
                    const branches2 = changes2.map((change) => ({
                      ...change.payload.doc.data(),
                      docid: change.payload.doc.id,
                    }));

                    // Combine and remove duplicates
                    const combinedBranches = [...branches1, ...branches2];
                    const uniqueBranches = this.removeDuplicates(
                      combinedBranches,
                      'docid'
                    );
                    observer.next(uniqueBranches);
                  });
                });
              }
            });
        } else {
          observer.next([]);
        }
      });
    });
  }

  private removeDuplicates(arr: any[], key: string) {
    const seen = new Set();
    return arr.filter((item) => {
      const k = item[key];
      return seen.has(k) ? false : seen.add(k);
    });
  }

  // Create a new draft contract (using local storage)
  async createDraftContract(
    officeId?: string,
    initialData?: Partial<Contract>
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            const contractId = this.generateLocalContractId();
            const contractNumber = this.generateLocalContractNumber();

            const contract: Contract = {
              id: contractId,
              contractNumber,
              status: ContractStatus.DRAFT,
              officeId: officeId || '', // Allow empty office ID for initial drafts
              currency: 'USD',
              createdAt: new Date(),
              updatedAt: new Date(),
              createdBy: user.uid,
              currentStep: 0,
              // Initialize payment fields
              paymentMethod: {
                type: 'Bank Transfer',
                billingCycle: 'Monthly',
                autoPay: false,
              },
              paymentStatus: 'No Payment Info',
              ...initialData,
            };

            console.log('Creating draft contract:', contract); // Debug log

            // Save to local storage instead of Firestore
            this.saveDraftToLocalStorage(contractId, contract);

            // Update office status to under_processing only if office is selected and not empty
            if (officeId && officeId.trim() !== '') {
              try {
                await this.updateOfficeStatus(officeId, 'under_processing');
              } catch (officeError) {
                console.warn('Could not update office status:', officeError);
                // Don't fail the entire operation if office update fails
              }
            }

            console.log('Draft contract created successfully:', contractId); // Debug log
            resolve(contractId);
          } catch (error) {
            console.error('Error creating draft contract:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Save draft contract data (using local storage)
  async saveDraftContract(
    contractId: string,
    data: Partial<Contract>,
    currentStep?: number
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            console.log('Saving draft contract:', contractId, data); // Debug log

            // Get existing draft from local storage
            const existingContract = this.getDraftFromLocalStorage(contractId);
            if (!existingContract) {
              console.error('Draft contract not found:', contractId);
              reject(new Error(`Draft contract not found: ${contractId}`));
              return;
            }

            const updateData: Partial<Contract> = {
              ...data,
              updatedAt: new Date(),
              lastModifiedBy: user.uid,
            };

            if (currentStep !== undefined) {
              updateData.currentStep = currentStep;
            }

            // Merge with existing contract data
            const updatedContract = {
              ...existingContract,
              ...updateData,
            };

            // Handle office selection changes
            const oldOfficeId = existingContract.officeId;
            const newOfficeId = updateData.officeId;

            // If office changed, update office statuses
            if (oldOfficeId !== newOfficeId) {
              try {
                // Reset old office status if it was set
                if (oldOfficeId && oldOfficeId !== '') {
                  await this.updateOfficeStatus(oldOfficeId, 'available');
                }
                // Set new office status if selected
                if (newOfficeId && newOfficeId !== '') {
                  await this.updateOfficeStatus(
                    newOfficeId,
                    'under_processing'
                  );
                }
              } catch (officeError) {
                console.warn('Could not update office statuses:', officeError);
                // Don't fail the entire save operation if office update fails
              }
            }

            // Save updated contract to local storage
            this.saveDraftToLocalStorage(contractId, updatedContract);

            console.log('Draft contract saved successfully:', contractId); // Debug log
            resolve();
          } catch (error) {
            console.error('Error saving draft contract:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Finalize contract (change from draft to active and move to Firestore)
  async finalizeContract(contractId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // Get the draft contract from local storage
            const contract = this.getDraftFromLocalStorage(contractId);
            if (!contract) {
              reject(new Error('Draft contract not found'));
              return;
            }

            // Ensure office is selected before finalizing
            if (!contract.officeId || contract.officeId === '') {
              reject(
                new Error('Office must be selected before finalizing contract')
              );
              return;
            }

            // Ensure tenant data exists
            if (!contract.tenant) {
              reject(
                new Error('Tenant data is required before finalizing contract')
              );
              return;
            }

            // Generate a new contract number for the finalized contract
            const contractNumber = await this.generateContractNumber();

            // Set payment information when finalizing contract
            // Assume client has paid when contract is finalized
            const now = new Date();
            const billingCycle =
              contract.paymentMethod?.billingCycle || 'Monthly';

            // Use the start date as the payment date if available, otherwise use current date
            // First check tenant lease details start date, then contract start date, then current date
            const paymentDate = contract.tenant?.leaseDetails?.terms?.startDate
              ? new Date(contract.tenant.leaseDetails.terms.startDate)
              : contract.startDate
              ? new Date(contract.startDate)
              : now;

            // Calculate next payment date based on billing cycle
            const nextPaymentAt = calculateNextPaymentDate(
              paymentDate,
              billingCycle
            );

            // Create the final contract in Firestore
            const finalContract: Contract = {
              ...contract,
              contractNumber,
              status: ContractStatus.ACTIVE,
              updatedAt: now,
              lastModifiedBy: user.uid,
              // Set payment information indicating client has paid
              paidAt: paymentDate,
              nextPaymentAt: nextPaymentAt,
              paymentStatus: 'Paid',
            };

            // Remove undefined values as Firebase doesn't support them
            const cleanedContract = this.removeUndefinedValues(finalContract);

            // Save contract to Firestore
            await this.afs
              .collection('contracts')
              .doc(contractId)
              .set(cleanedContract);

            // Create tenant record in Firebase and get the tenant ID
            const tenantId = await this.createTenantRecord(
              contract.tenant,
              contractId,
              user.uid
            );

            // Update the contract with the tenant ID
            await this.afs
              .collection('contracts')
              .doc(contractId)
              .update({ tenantId: tenantId });

            // Record the initial payment in the database
            try {
              const rentAmount =
                contract.tenant?.leaseDetails?.terms?.rentAmount ||
                contract.rentAmount ||
                1000; // fallback amount

              const paymentMethodType =
                contract.tenant?.leaseDetails?.paymentMethod?.type ||
                contract.paymentMethod?.type ||
                'Bank Transfer';

              // Format the payment period based on billing cycle and payment date
              const paymentPeriod = this.formatPaymentPeriod(
                paymentDate,
                billingCycle
              );

              const paymentRecord = {
                contractId: contractId,
                tenantId: tenantId,
                amount: rentAmount,
                currency: contract.currency || 'USD',
                paymentDate: paymentDate,
                dueDate: paymentDate, // For initial payment, due date is same as payment date
                paymentMethod: paymentMethodType,
                status: 'Paid' as const,
                period: paymentPeriod,
                notes: 'Initial payment recorded upon contract finalization',
              };

              await this.createPaymentRecord(paymentRecord);
              console.log('Initial payment record created successfully');
            } catch (paymentRecordError) {
              console.error(
                'Error creating initial payment record:',
                paymentRecordError
              );
              // Don't fail the entire finalization process if payment record creation fails
              // The contract is still finalized successfully
            }

            // Remove from local storage
            this.removeDraftFromLocalStorage(contractId);

            // Update office status to occupied
            await this.updateOfficeStatus(contract.officeId, 'occupied');

            // Send client invitation based on selected method
            const registrationLinkMethod =
              contract.tenant.legal?.registrationLinkMethod || 'Email';
            try {
              await this.sendClientInvitation(tenantId, registrationLinkMethod);
              console.log(
                `Client invitation sent via ${registrationLinkMethod}`
              );
            } catch (inviteError) {
              console.warn('Could not send client invitation:', inviteError);
              // Don't fail the entire operation if invitation sending fails
            }

            resolve();
          } catch (error) {
            console.error('Error finalizing contract:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Fetch all deleted contracts with associated tenant IDs
  getDeletedContracts(): Observable<Contract[]> {
    return this.afs
      .collection<Contract>('contracts', (ref) =>
        ref.where('status', '==', ContractStatus.DELETED)
      )
      .valueChanges({ idField: 'id' })
      .pipe(
        switchMap(async (contracts) => {
          // For each contract, find the associated tenant ID
          const contractsWithTenantIds = await Promise.all(
            contracts.map(async (contract) => {
              try {
                // Find tenant by contractId
                const tenantQuery = await firstValueFrom(
                  this.afs
                    .collection('tenants', (ref) =>
                      ref
                        .where('contractId', '==', contract.id)
                        .where('isDeleted', '==', true)
                    )
                    .get()
                );

                if (!tenantQuery.empty) {
                  const tenantDoc = tenantQuery.docs[0];
                  const tenantData = tenantDoc.data();
                  if (tenantData) {
                    return {
                      ...contract,
                      tenantId: tenantDoc.id, // Add the tenant document ID
                      tenant: {
                        ...(tenantData as any),
                        id: tenantDoc.id, // Ensure the tenant object has the ID
                      },
                    };
                  }
                }
                return contract;
              } catch (error) {
                console.error(
                  'Error fetching tenant for contract:',
                  contract.id,
                  error
                );
                return contract;
              }
            })
          );
          return contractsWithTenantIds;
        })
      );
  }

  // Create tenant record in Firebase
  private async createTenantRecord(
    tenantData: any,
    contractId: string,
    userId: string
  ): Promise<string> {
    try {
      // Generate a unique tenant ID
      const tenantId = this.afs.createId();

      // Fetch the receptionist's user document to get ownerId and managerId
      const userDoc = await this.afs
        .collection('users')
        .doc(userId)
        .get()
        .toPromise();
      const userData = userDoc?.data() as any;
      const ownerId = userData?.adminId || userData?.ownerId || userId; // fallback to self if not found
      const managerId = userData?.managerId || null;

      // Create tenant document with additional metadata
      const tenant = {
        ...tenantData,
        id: tenantId,
        contractId: contractId,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: userId,
        ownerId: ownerId,
        managerId: managerId,
        isDeleted: false,
      };

      // Save tenant to Firestore
      await this.afs.collection('tenants').doc(tenantId).set(tenant);

      console.log('Tenant created successfully:', tenantId);
      return tenantId;
    } catch (error) {
      console.error('Error creating tenant record:', error);
      throw error;
    }
  }

  // Get all tenants for the current user (filtered by owner/manager)
  getAllTenantsForUser(user: any): Observable<TenantFromDatabase[]> {
    return new Observable<TenantFromDatabase[]>((observer) => {
      this.afAuth.authState.subscribe((authUser) => {
        if (authUser) {
          let ownerId =
            user.role === 'owner' ? user.uid : user.adminId || user.ownerId;
          if (!ownerId) {
            console.error('No ownerId found for user:', user);
            observer.next([]);
            return;
          }
          let query = this.afs.collection('tenants', (ref) => {
            let q = ref.where('ownerId', '==', ownerId);
            return q;
          });
          query
            .valueChanges({ idField: 'id' })
            .subscribe(async (tenants: any[]) => {
              try {
                const typedTenants = tenants as TenantFromDatabase[];

                // Get contracts for these tenants to filter by contract status
                const tenantsWithActiveContracts: TenantFromDatabase[] = [];

                // Collect all contract IDs first
                const contractIds = typedTenants
                  .filter((tenant) => tenant.contractId)
                  .map((tenant) => tenant.contractId);

                if (contractIds.length === 0) {
                  observer.next([]);
                  return;
                }

                // Batch fetch all contracts at once using Promise.all (safer than where-in query)
                const contractPromises = contractIds.map((contractId) =>
                  this.afs
                    .collection('contracts')
                    .doc(contractId)
                    .get()
                    .toPromise()
                );

                const contractDocs = await Promise.all(contractPromises);

                // Create a map of contractId -> contract for fast lookups
                const contractMap = new Map<string, Contract>();
                contractDocs.forEach((doc, index) => {
                  if (doc && doc.exists) {
                    const contract = doc.data() as Contract;
                    if (
                      [
                        ContractStatus.ACTIVE,
                        ContractStatus.DRAFT,
                        ContractStatus.PAUSED,
                      ].includes(contract.status)
                    ) {
                      contractMap.set(contractIds[index], contract);
                    }
                  }
                });

                // Filter tenants using the contract map
                for (const tenant of typedTenants) {
                  if (tenant.contractId && contractMap.has(tenant.contractId)) {
                    tenantsWithActiveContracts.push(tenant);
                  }
                }

                observer.next(tenantsWithActiveContracts);
              } catch (error) {
                console.error(
                  'Error filtering tenants by contract status:',
                  error
                );
                observer.next([]);
              }
            });
        } else {
          observer.next([]);
        }
      });
    });
  }

  // Cancel draft contract (remove from local storage)
  async cancelDraftContract(contractId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            const contract = this.getDraftFromLocalStorage(contractId);
            if (!contract) {
              reject(new Error('Draft contract not found'));
              return;
            }

            // Remove from local storage
            this.removeDraftFromLocalStorage(contractId);

            // Reset office status to available only if office was selected
            if (contract.officeId && contract.officeId !== '') {
              await this.updateOfficeStatus(contract.officeId, 'available');
            }

            resolve();
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Get contract by ID (check local storage first for drafts)
  getContract(contractId: string): Observable<Contract | undefined> {
    console.log('getContract called with contractId:', contractId); // Debug log

    // For draft contracts, use the same method as getDraftContracts for consistency
    if (contractId.startsWith('draft_')) {
      return new Observable<Contract | undefined>((observer) => {
        const allDrafts = this.getAllDraftsFromLocalStorage();
        console.log('All drafts from localStorage:', allDrafts); // Debug log
        console.log('Looking for contractId:', contractId); // Debug log

        const foundContract = allDrafts.find(
          (draft) => draft.id === contractId
        );
        console.log('Found contract:', foundContract); // Debug log

        observer.next(foundContract || undefined);
        observer.complete();
      });
    }

    console.log('Not a draft, checking Firestore'); // Debug log
    // If not a draft, check Firestore
    return this.afs
      .collection('contracts')
      .doc<Contract>(contractId)
      .valueChanges({ idField: 'id' });
  }

  // Get contract with populated office and tenant data
  getContractWithDetails(contractId: string): Observable<Contract | undefined> {
    return this.getContract(contractId).pipe(
      map((contract) => {
        if (!contract) return undefined;

        // You can extend this to populate office and tenant details
        // For now, returning the contract as is
        return contract;
      })
    );
  }

  // Get all contracts
  getAllContracts(): Observable<Contract[]> {
    return this.afs
      .collection<Contract>('contracts', (ref) =>
        ref.orderBy('createdAt', 'desc')
      )
      .valueChanges({ idField: 'id' });
  }

  // Get all tenants from Firebase
  getAllTenants(): Observable<any[]> {
    return new Observable<any[]>((observer) => {
      this.afAuth.authState.subscribe((user) => {
        if (user) {
          this.afs
            .collection('tenants', (ref) => ref.orderBy('createdAt', 'desc'))
            .valueChanges({ idField: 'id' })
            .subscribe((tenants) => {
              observer.next(tenants);
            });
        } else {
          observer.next([]);
        }
      });
    });
  }

  // Get tenant by ID
  getTenant(tenantId: string): Observable<any | undefined> {
    return this.afs
      .collection('tenants')
      .doc(tenantId)
      .valueChanges({ idField: 'id' });
  }

  // Update tenant
  async updateTenant(tenantId: string, tenantData: any): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            await this.afs
              .collection('tenants')
              .doc(tenantId)
              .update({
                ...tenantData,
                updatedAt: new Date(),
                lastModifiedBy: user.uid,
              });
            resolve();
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }
  //restore contract and tenant
  restoreContract(contractId: string): Observable<void> {
    return from(
      this.afs
        .collection('contracts')
        .doc(contractId)
        .update({ status: ContractStatus.ACTIVE })
    );
  }

  restoreTenant(tenantId: string): Observable<void> {
    console.log(`Attempting to restore tenant with ID: ${tenantId}`);
    return from(
      this.afs.collection('tenants').doc(tenantId).update({ isDeleted: false })
    ).pipe(
      tap(() =>
        console.log(`Tenant with ID: ${tenantId} restored successfully`)
      ),
      catchError((error) => {
        console.error(`Error restoring tenant with ID: ${tenantId}`, error);
        return throwError(error);
      })
    );
  }

  // Delete tenant
  async deleteTenant(tenantId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // Get tenant data first to find associated contract
            const tenantDoc = await firstValueFrom(
              this.afs.collection('tenants').doc(tenantId).get()
            );

            if (tenantDoc.exists) {
              const tenantData = tenantDoc.data() as any;
              const contractId = tenantData?.contractId;

              // Update the isDeleted flag to true instead of deleting the tenant
              await this.afs.collection('tenants').doc(tenantId).update({
                isDeleted: true,
                updatedAt: new Date(),
                lastModifiedBy: user.uid,
              });

              // If there's an associated contract, update its status and office
              if (contractId) {
                try {
                  // Get the contract to find the office
                  const contractDoc = await firstValueFrom(
                    this.afs.collection('contracts').doc(contractId).get()
                  );

                  if (contractDoc.exists) {
                    const contractData = contractDoc.data() as any;

                    // Update contract status to cancelled
                    await this.afs
                      .collection('contracts')
                      .doc(contractId)
                      .update({
                        status: ContractStatus.DELETED,
                        updatedAt: new Date(),
                        lastModifiedBy: user.uid,
                      });

                    // Update office status back to available
                    if (contractData?.officeId) {
                      await this.updateOfficeStatus(
                        contractData.officeId,
                        'available'
                      );
                    }
                  }
                } catch (contractError) {
                  console.warn(
                    'Could not update associated contract:',
                    contractError
                  );
                  // Continue with tenant deletion even if contract update fails
                }
              }

              resolve();
            } else {
              reject(new Error('Tenant not found'));
            }
          } catch (error) {
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Get draft contracts (from local storage)
  getDraftContracts(): Observable<Contract[]> {
    return new Observable<Contract[]>((observer) => {
      const drafts = this.getAllDraftsFromLocalStorage();
      observer.next(drafts);
      observer.complete();
    });
  }

  // Update office status
  private async updateOfficeStatus(
    officeId: string,
    status: 'available' | 'occupied' | 'under_processing' | 'maintenance'
  ): Promise<void> {
    await this.afs.collection('offices').doc(officeId).update({
      status,
      updatedAt: new Date(),
    });
  }

  // Generate unique contract number
  private async generateContractNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');

    // Get the count of contracts for this month
    const contractsSnapshot = await firstValueFrom(
      this.afs
        .collection('contracts', (ref) =>
          ref
            .where('createdAt', '>=', new Date(year, new Date().getMonth(), 1))
            .where(
              'createdAt',
              '<',
              new Date(year, new Date().getMonth() + 1, 1)
            )
        )
        .get()
    );

    const count = (contractsSnapshot?.size || 0) + 1;
    const contractNumber = `CON-${year}${month}-${String(count).padStart(
      4,
      '0'
    )}`;

    return contractNumber;
  }

  // Local storage helper methods for draft contracts
  private readonly DRAFT_CONTRACTS_KEY = 'draft_contracts';

  private generateLocalContractId(): string {
    return (
      'draft_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    );
  }

  private generateLocalContractNumber(): string {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-4);
    return `DRAFT-${year}${month}-${timestamp}`;
  }

  private saveDraftToLocalStorage(
    contractId: string,
    contract: Contract
  ): void {
    try {
      console.log('Saving draft to localStorage:', contractId, contract); // Debug log

      // Validate contract data before saving
      if (!contractId || !contract) {
        throw new Error('Invalid contract data provided');
      }

      const existingDrafts = this.getAllDraftsFromLocalStorage();
      const draftsMap = new Map(existingDrafts.map((d) => [d.id!, d]));
      draftsMap.set(contractId, contract);

      const draftsArray = Array.from(draftsMap.values());

      // Check if localStorage has enough space
      const dataToSave = JSON.stringify(draftsArray);
      if (dataToSave.length > 5000000) {
        // ~5MB limit
        console.warn('Draft data is getting large, consider cleanup');
      }

      localStorage.setItem(this.DRAFT_CONTRACTS_KEY, dataToSave);
      console.log('Draft saved successfully to localStorage'); // Debug log
    } catch (error) {
      console.error('Error saving draft to local storage:', error);

      // If localStorage is full, try to clean up old drafts
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        console.log('Attempting to clean up old drafts...');
        try {
          const drafts = this.getAllDraftsFromLocalStorage();
          // Keep only the last 10 drafts
          const recentDrafts = drafts.slice(0, 10);
          localStorage.setItem(
            this.DRAFT_CONTRACTS_KEY,
            JSON.stringify(recentDrafts)
          );

          // Try saving again
          this.saveDraftToLocalStorage(contractId, contract);
        } catch (cleanupError) {
          console.error('Failed to cleanup and retry save:', cleanupError);
          throw new Error(
            'Failed to save draft: storage is full and cleanup failed'
          );
        }
      } else {
        throw error;
      }
    }
  }

  getDraftFromLocalStorage(contractId: string): Contract | null {
    // Use the same method as getAllDraftsFromLocalStorage for consistency
    const allDrafts = this.getAllDraftsFromLocalStorage();
    return allDrafts.find((draft) => draft.id === contractId) || null;
  }

  private getAllDraftsFromLocalStorage(): Contract[] {
    try {
      const draftsJson = localStorage.getItem(this.DRAFT_CONTRACTS_KEY);
      if (!draftsJson) return [];

      const drafts = JSON.parse(draftsJson) as Contract[];

      // Convert date strings back to Date objects
      const processedDrafts = drafts
        .map((draft) => ({
          ...draft,
          createdAt: draft.createdAt ? new Date(draft.createdAt) : new Date(),
          updatedAt: draft.updatedAt ? new Date(draft.updatedAt) : new Date(),
          startDate: draft.startDate ? new Date(draft.startDate) : undefined,
          endDate: draft.endDate ? new Date(draft.endDate) : undefined,
          paidAt: draft.paidAt ? new Date(draft.paidAt) : undefined,
          nextPaymentAt: draft.nextPaymentAt
            ? new Date(draft.nextPaymentAt)
            : undefined,
        }))
        .sort(
          (a, b) =>
            (b.updatedAt?.getTime() || 0) - (a.updatedAt?.getTime() || 0)
        );

      console.log(
        'getAllDraftsFromLocalStorage - processed drafts:',
        processedDrafts
      ); // Debug log
      return processedDrafts;
    } catch (error) {
      console.error('Error getting all drafts from local storage:', error);
      return [];
    }
  }

  private removeDraftFromLocalStorage(contractId: string): void {
    try {
      const drafts = this.getAllDraftsFromLocalStorage();
      const filteredDrafts = drafts.filter((draft) => draft.id !== contractId);
      localStorage.setItem(
        this.DRAFT_CONTRACTS_KEY,
        JSON.stringify(filteredDrafts)
      );
    } catch (error) {
      console.error('Error removing draft from local storage:', error);
    }
  }

  // Clear all draft contracts from local storage (utility method)
  clearAllDraftsFromLocalStorage(): void {
    try {
      localStorage.removeItem(this.DRAFT_CONTRACTS_KEY);
    } catch (error) {
      console.error('Error clearing drafts from local storage:', error);
    }
  }

  // Debug method to inspect localStorage data
  debugLocalStorageData(): void {
    try {
      const draftsJson = localStorage.getItem(this.DRAFT_CONTRACTS_KEY);
      console.log('Raw localStorage data:', draftsJson);
      if (draftsJson) {
        const drafts = JSON.parse(draftsJson);
        console.log('Parsed drafts:', drafts);
      }
    } catch (error) {
      console.error('Error debugging localStorage:', error);
    }
  }

  // Payment-related methods

  /**
   * Record a payment for a contract
   * @param contractId The contract ID
   * @param paymentDate The date of the payment
   * @param billingCycle The billing cycle (optional)
   * @returns Promise<void>
   */
  async recordPayment(
    contractId: string,
    paymentDate: Date,
    billingCycle?: 'Monthly' | 'Quarterly'
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // Validate input
            if (!paymentDate || !(paymentDate instanceof Date)) {
              throw new Error('Invalid payment date provided');
            }

            // Ensure the payment date is a proper Date object
            const validPaymentDate = new Date(paymentDate);
            if (isNaN(validPaymentDate.getTime())) {
              throw new Error('Invalid payment date format');
            }

            // First check if it's a draft contract
            const draftContract = this.getDraftFromLocalStorage(contractId);

            if (draftContract) {
              // Update draft contract
              const paymentUpdate = updateContractPaymentInfo(
                draftContract,
                validPaymentDate,
                billingCycle
              );
              await this.saveDraftContract(contractId, paymentUpdate);
            } else {
              // Update Firestore contract
              const contractRef = this.afs
                .collection('contracts')
                .doc(contractId);
              const contractDoc = await firstValueFrom(contractRef.get());

              if (!contractDoc.exists) {
                throw new Error('Contract not found');
              }

              const contract = contractDoc.data() as Contract;
              const paymentUpdate = updateContractPaymentInfo(
                contract,
                validPaymentDate,
                billingCycle
              );

              // Ensure all date fields are proper Date objects before updating
              const updateData: any = {
                ...paymentUpdate,
                updatedAt: new Date(),
                lastModifiedBy: user.uid,
              };

              // Clean the update data to ensure proper Firestore formatting
              const cleanedUpdateData = this.removeUndefinedValues(updateData);

              await contractRef.update(cleanedUpdateData);
            }

            console.log(
              'Payment recorded successfully for contract:',
              contractId
            );
            resolve();
          } catch (error) {
            console.error('Error recording payment:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  /**
   * Update billing cycle for a contract
   * @param contractId The contract ID
   * @param billingCycle The new billing cycle
   * @returns Promise<void>
   */
  async updateBillingCycle(
    contractId: string,
    billingCycle: 'Monthly' | 'Quarterly'
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // First check if it's a draft contract
            const draftContract = this.getDraftFromLocalStorage(contractId);

            if (draftContract) {
              // Update draft contract
              const updatedPaymentMethod = {
                type: 'Bank Transfer' as const,
                autoPay: false,
                ...draftContract.paymentMethod,
                billingCycle,
              };

              const updateData: Partial<Contract> = {
                paymentMethod: updatedPaymentMethod,
                paymentStatus: computePaymentStatus({
                  ...draftContract,
                  paymentMethod: updatedPaymentMethod,
                }),
              };

              // Recalculate next payment date if we have a last payment date
              if (draftContract.paidAt) {
                const paymentUpdate = updateContractPaymentInfo(
                  draftContract,
                  new Date(draftContract.paidAt), // Ensure it's a proper Date object
                  billingCycle
                );
                Object.assign(updateData, paymentUpdate);
              }

              await this.saveDraftContract(contractId, updateData);
            } else {
              // Update Firestore contract
              const contractRef = this.afs
                .collection('contracts')
                .doc(contractId);
              const contractDoc = await firstValueFrom(contractRef.get());

              if (!contractDoc.exists) {
                throw new Error('Contract not found');
              }

              const contract = contractDoc.data() as Contract;
              const updatedPaymentMethod = {
                type: 'Bank Transfer' as const,
                autoPay: false,
                ...contract.paymentMethod,
                billingCycle,
              };

              const updateData: Partial<Contract> = {
                paymentMethod: updatedPaymentMethod,
                paymentStatus: computePaymentStatus({
                  ...contract,
                  paymentMethod: updatedPaymentMethod,
                }),
                updatedAt: new Date(),
                lastModifiedBy: user.uid,
              };

              // Recalculate next payment date if we have a last payment date
              if (contract.paidAt) {
                const paymentUpdate = updateContractPaymentInfo(
                  contract,
                  new Date(contract.paidAt), // Ensure it's a proper Date object
                  billingCycle
                );
                Object.assign(updateData, paymentUpdate);
              }

              // Clean the update data to ensure proper Firestore formatting
              const cleanedUpdateData = this.removeUndefinedValues(updateData);

              await contractRef.update(cleanedUpdateData);
            }

            resolve();
          } catch (error) {
            console.error('Error updating payment plan:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  /**
   * Refresh payment statuses for all contracts (batch operation)
   * This method can be called periodically to update payment statuses
   * @returns Promise<void>
   */
  async refreshAllPaymentStatuses(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // Refresh draft contracts
            const drafts = this.getAllDraftsFromLocalStorage();
            for (const draft of drafts) {
              if (draft.id) {
                const statusUpdate = refreshContractPaymentStatus(draft);
                await this.saveDraftContract(draft.id, statusUpdate);
              }
            }

            // Refresh Firestore contracts
            const contractsSnapshot = await firstValueFrom(
              this.afs
                .collection('contracts', (ref) =>
                  ref.where('status', '==', ContractStatus.ACTIVE)
                )
                .get()
            );

            const batch = this.afs.firestore.batch();
            contractsSnapshot.docs.forEach((doc) => {
              const contract = doc.data() as Contract;
              const statusUpdate = refreshContractPaymentStatus(contract);

              if (statusUpdate.paymentStatus !== contract.paymentStatus) {
                // Clean the update data to ensure proper Firestore formatting
                const cleanedUpdateData = this.removeUndefinedValues({
                  ...statusUpdate,
                  updatedAt: new Date(),
                  lastModifiedBy: user.uid,
                });

                batch.update(doc.ref, cleanedUpdateData);
              }
            });

            await batch.commit();
            resolve();
          } catch (error) {
            console.error('Error refreshing payment statuses:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  /**
   * Get contracts by payment status
   * @param paymentStatus The payment status to filter by
   * @returns Observable<Contract[]>
   */
  getContractsByPaymentStatus(paymentStatus: string): Observable<Contract[]> {
    return this.afs
      .collection<Contract>('contracts', (ref) =>
        ref
          .where('paymentStatus', '==', paymentStatus)
          .where('status', '==', ContractStatus.ACTIVE)
      )
      .valueChanges({ idField: 'id' });
  }

  /**
   * Get contracts with upcoming payments (due within specified days)
   * @param daysAhead Number of days to look ahead (default: 7)
   * @returns Observable<Contract[]>
   */
  getContractsWithUpcomingPayments(
    daysAhead: number = 7
  ): Observable<Contract[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);

    return this.afs
      .collection<Contract>('contracts', (ref) =>
        ref
          .where('nextPaymentAt', '<=', futureDate)
          .where('nextPaymentAt', '>=', new Date())
          .where('status', '==', ContractStatus.ACTIVE)
      )
      .valueChanges({ idField: 'id' });
  }

  /**
   * Get overdue contracts
   * @returns Observable<Contract[]>
   */
  getOverdueContracts(): Observable<Contract[]> {
    return this.afs
      .collection<Contract>('contracts', (ref) =>
        ref
          .where('paymentStatus', '==', 'Overdue')
          .where('status', '==', ContractStatus.ACTIVE)
      )
      .valueChanges({ idField: 'id' });
  }

  /**
   * Get all active contracts
   * @returns Observable<Contract[]>
   */
  getActiveContracts(): Observable<Contract[]> {
    return this.afs
      .collection<Contract>('contracts', (ref) =>
        ref.where('status', '==', ContractStatus.ACTIVE)
      )
      .valueChanges({ idField: 'id' });
  }

  /**
   * Fix existing contracts that have invalid date fields (arrays instead of dates)
   * This is a utility method to clean up existing data
   * @returns Promise<void>
   */
  async fixInvalidDateFields(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            console.log('Starting to fix invalid date fields in contracts...');

            // Get all contracts
            const contractsSnapshot = await firstValueFrom(
              this.afs.collection('contracts').get()
            );

            const batch = this.afs.firestore.batch();
            let updatedCount = 0;

            contractsSnapshot.docs.forEach((doc) => {
              const contract = doc.data() as any;
              let needsUpdate = false;
              const updates: any = {};

              // Check paidAt field
              if (contract.paidAt && Array.isArray(contract.paidAt)) {
                console.log(
                  `Contract ${doc.id} has paidAt as array:`,
                  contract.paidAt
                );
                // Remove the invalid array field
                updates.paidAt = null;
                needsUpdate = true;
              }

              // Check nextPaymentAt field
              if (
                contract.nextPaymentAt &&
                Array.isArray(contract.nextPaymentAt)
              ) {
                console.log(
                  `Contract ${doc.id} has nextPaymentAt as array:`,
                  contract.nextPaymentAt
                );
                // Remove the invalid array field
                updates.nextPaymentAt = null;
                needsUpdate = true;
              }

              // If the contract has valid start date, use it to set initial payment info
              if (needsUpdate && contract.startDate) {
                const startDate = this.ensureValidDate(contract.startDate);
                const billingCycle =
                  contract.paymentMethod?.billingCycle || 'Monthly';

                if (startDate) {
                  const nextPayment = calculateNextPaymentDate(
                    startDate,
                    billingCycle
                  );
                  updates.paidAt = startDate;
                  updates.nextPaymentAt = nextPayment;
                  updates.paymentStatus = 'Paid';
                  console.log(
                    `Setting proper payment dates for contract ${doc.id}:`,
                    {
                      paidAt: startDate,
                      nextPaymentAt: nextPayment,
                    }
                  );
                }
              }

              if (needsUpdate) {
                updates.updatedAt = new Date();
                updates.lastModifiedBy = user.uid;

                // Clean the update data
                const cleanedUpdates = this.removeUndefinedValues(updates);
                batch.update(doc.ref, cleanedUpdates);
                updatedCount++;
              }
            });

            if (updatedCount > 0) {
              await batch.commit();
              console.log(
                `Fixed ${updatedCount} contracts with invalid date fields.`
              );
            } else {
              console.log('No contracts found with invalid date fields.');
            }

            resolve();
          } catch (error) {
            console.error('Error fixing invalid date fields:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  /**
   * Ensure a value is a valid Date object
   * @param value The value to convert to Date
   * @returns A valid Date object or null if invalid
   */
  private ensureValidDate(value: any): Date | null {
    if (!value) return null;

    if (value instanceof Date) {
      return isNaN(value.getTime()) ? null : value;
    }

    if (typeof value === 'string' || typeof value === 'number') {
      const date = new Date(value);
      return isNaN(date.getTime()) ? null : date;
    }

    return null;
  }

  /**
   * Remove undefined values from an object to make it Firebase-compatible
   * @param obj The object to clean
   * @returns A new object with undefined values removed
   */
  private removeUndefinedValues(obj: any): any {
    if (obj === null || obj === undefined) {
      return null;
    }

    // Handle Date objects properly - don't process them as arrays
    if (obj instanceof Date) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.removeUndefinedValues(item));
    }

    if (typeof obj === 'object') {
      const cleaned: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key) && obj[key] !== undefined) {
          // Special handling for date fields to ensure they're proper Date objects
          if (
            (key === 'paidAt' ||
              key === 'nextPaymentAt' ||
              key === 'startDate' ||
              key === 'endDate' ||
              key === 'createdAt' ||
              key === 'updatedAt') &&
            obj[key]
          ) {
            const validDate = this.ensureValidDate(obj[key]);
            if (validDate) {
              cleaned[key] = validDate;
            }
          } else {
            cleaned[key] = this.removeUndefinedValues(obj[key]);
          }
        }
      }
      return cleaned;
    }

    return obj;
  }

  /**
   * Public method to trigger the fix for invalid date fields
   * Can be called from components or admin panels
   * @returns Promise<void>
   */
  public async triggerDateFieldsFix(): Promise<void> {
    try {
      await this.fixInvalidDateFields();
      console.log('Date fields fix completed successfully');
    } catch (error) {
      console.error('Failed to fix date fields:', error);
      throw error;
    }
  }

  // Payment Timeline API Method - Fetch real payment records
  getPaymentTimeline(contractId: string): Observable<PaymentTimelineEvent[]> {
    return this.afAuth.authState.pipe(
      switchMap((user) => {
        if (!user) {
          return throwError(() => new Error('User not authenticated'));
        }

        // Fetch payment records for this contract
        return this.afs
          .collection<PaymentRecord>('payments', (ref) =>
            ref.where('contractId', '==', contractId)
          )
          .valueChanges({ idField: 'id' });
      }),
      switchMap((paymentRecords) => {
        // Also get contract details to generate missing payment periods
        return this.afs
          .doc<Contract>(`contracts/${contractId}`)
          .valueChanges({ idField: 'id' })
          .pipe(
            map((contract) => ({
              paymentRecords,
              contract,
            }))
          );
      }),
      map(({ paymentRecords, contract }) => {
        if (!contract) {
          return [];
        }

        // Convert payment records to timeline events and add missing periods
        return this.generateCompletePaymentTimeline(paymentRecords, contract);
      }),
      catchError((error) => {
        console.error('Error fetching payment timeline:', error);
        return throwError(() => error);
      })
    );
  }

  private generateCompletePaymentTimeline(
    paymentRecords: PaymentRecord[],
    contract: Contract
  ): PaymentTimelineEvent[] {
    const timeline: PaymentTimelineEvent[] = [];
    const today = new Date();

    // First, convert existing payment records to timeline events
    paymentRecords.forEach((payment) => {
      const paymentDate = this.convertToDate(payment.paymentDate);
      const dueDate = this.convertToDate(payment.dueDate);

      timeline.push({
        id: payment.id,
        date: paymentDate,
        dueDate: dueDate,
        period: payment.period,
        status: payment.status,
        amount: payment.amount,
        currency: payment.currency,
        paymentMethod: payment.paymentMethod,
        notes: payment.notes,
        transactionId: payment.transactionId,
        chequeNumber: payment.chequeNumber,
      });
    });

    // Generate expected payment periods if we have contract details
    if (contract.startDate && contract.paymentMethod?.billingCycle) {
      const expectedPayments = this.generateExpectedPaymentPeriods(contract);

      // Add missing payment periods that don't have records yet
      expectedPayments.forEach((expected) => {
        const existingRecord = paymentRecords.find(
          (payment) => payment.period === expected.period
        );

        if (!existingRecord) {
          timeline.push(expected);
        }
      });
    }

    // Sort timeline by due date (newest first)
    return timeline.sort((a, b) => {
      const dateA = a.dueDate || a.date;
      const dateB = b.dueDate || b.date;
      return dateB.getTime() - dateA.getTime();
    });
  }

  private generateExpectedPaymentPeriods(
    contract: Contract
  ): PaymentTimelineEvent[] {
    const expectedPayments: PaymentTimelineEvent[] = [];

    if (!contract.startDate || !contract.paymentMethod?.billingCycle) {
      return [];
    }

    const startDate = this.convertToDate(contract.startDate);
    const endDate = contract.endDate
      ? this.convertToDate(contract.endDate)
      : new Date(
          startDate.getFullYear() + 1,
          startDate.getMonth(),
          startDate.getDate()
        );
    const today = new Date();

    let currentDate = new Date(startDate);
    const billingCycle = contract.paymentMethod.billingCycle;

    while (currentDate <= endDate) {
      const dueDate = new Date(currentDate);
      let period: string;

      // Generate period label based on billing cycle
      if (billingCycle === 'Quarterly') {
        const quarter = Math.floor(dueDate.getMonth() / 3) + 1;
        period = `Q${quarter} ${dueDate.getFullYear()}`;
      } else if (billingCycle === 'Yearly') {
        period = `${dueDate.getFullYear()}`;
      } else {
        period = dueDate.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric',
        });
      }

      // Determine status based on current date
      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      let status: 'Paid' | 'Overdue' | 'Soon' | 'Upcoming';
      if (diffDays < 0) {
        status = 'Overdue';
      } else if (diffDays <= 7) {
        status = 'Soon';
      } else {
        status = 'Upcoming';
      }

      expectedPayments.push({
        date: dueDate,
        dueDate: dueDate,
        period: period,
        status: status,
        amount: contract.rentAmount || 0,
        currency: contract.currency || 'USD',
        paymentMethod: contract.paymentMethod?.type,
        notes:
          status === 'Overdue'
            ? 'Payment overdue - no record found'
            : status === 'Soon'
            ? 'Payment due soon'
            : 'Future payment expected',
      });

      // Move to next payment date based on billing cycle
      if (billingCycle === 'Quarterly') {
        currentDate.setMonth(currentDate.getMonth() + 3);
      } else if (billingCycle === 'Yearly') {
        currentDate.setFullYear(currentDate.getFullYear() + 1);
      } else {
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    return expectedPayments;
  }

  // Helper method to convert Firestore Timestamp to Date
  private convertToDate(timestamp: any): Date {
    if (!timestamp) return new Date();

    // If it's already a Date object
    if (timestamp instanceof Date) {
      return timestamp;
    }

    // If it's a Firestore Timestamp with seconds property
    if (timestamp.seconds) {
      return new Date(timestamp.seconds * 1000);
    }

    // If it has a toDate method (Firestore Timestamp)
    if (typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }

    // Fallback: try to parse as regular date
    return new Date(timestamp);
  }

  // Helper method to format payment period based on date and billing cycle
  private formatPaymentPeriod(paymentDate: Date, billingCycle: string): string {
    const date = new Date(paymentDate);
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];

    switch (billingCycle) {
      case 'Monthly':
        return `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
      case 'Quarterly':
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `Q${quarter} ${date.getFullYear()}`;
      case 'Yearly':
        return `${date.getFullYear()}`;
      default:
        return `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
    }
  }

  // Payment Management Methods

  // Create a new payment record
  async createPaymentRecord(
    paymentData: Omit<
      PaymentRecord,
      'id' | 'createdAt' | 'updatedAt' | 'createdBy'
    >
  ): Promise<string> {
    const user = await firstValueFrom(this.afAuth.authState);
    if (!user) {
      throw new Error('User not authenticated');
    }

    const paymentRecord: PaymentRecord = {
      ...paymentData,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: user.uid,
    };

    const docRef = await this.afs.collection('payments').add(paymentRecord);

    // Update contract payment status
    if (paymentData.contractId) {
      await this.updateContractPaymentStatus(paymentData.contractId);
    }

    return docRef.id;
  }

  // Update an existing payment record
  async updatePaymentRecord(
    paymentId: string,
    updateData: Partial<PaymentRecord>
  ): Promise<void> {
    const user = await firstValueFrom(this.afAuth.authState);
    if (!user) {
      throw new Error('User not authenticated');
    }

    const updates = {
      ...updateData,
      updatedAt: new Date(),
    };

    await this.afs.doc(`payments/${paymentId}`).update(updates);

    // Update contract payment status if contract was changed
    if (updateData.contractId) {
      await this.updateContractPaymentStatus(updateData.contractId);
    }
  }

  // Delete a payment record
  async deletePaymentRecord(paymentId: string): Promise<void> {
    const user = await firstValueFrom(this.afAuth.authState);
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get payment record first to update contract status afterwards
    const paymentDoc = await firstValueFrom(
      this.afs.doc<PaymentRecord>(`payments/${paymentId}`).valueChanges()
    );

    await this.afs.doc(`payments/${paymentId}`).delete();

    // Update contract payment status
    if (paymentDoc?.contractId) {
      await this.updateContractPaymentStatus(paymentDoc.contractId);
    }
  }

  // Get all payment records for a contract
  getContractPayments(contractId: string): Observable<PaymentRecord[]> {
    return this.afs
      .collection<PaymentRecord>('payments', (ref) =>
        ref.where('contractId', '==', contractId)
      )
      .valueChanges({ idField: 'id' })
      .pipe(
        map((payments) =>
          payments.sort(
            (a, b) =>
              this.convertToDate(b.dueDate).getTime() -
              this.convertToDate(a.dueDate).getTime()
          )
        )
      );
  }

  // Get all payment records for a tenant
  getTenantPayments(tenantId: string): Observable<PaymentRecord[]> {
    return this.afs
      .collection<PaymentRecord>('payments', (ref) =>
        ref.where('tenantId', '==', tenantId)
      )
      .valueChanges({ idField: 'id' })
      .pipe(
        map((payments) =>
          payments.sort(
            (a, b) =>
              this.convertToDate(b.dueDate).getTime() -
              this.convertToDate(a.dueDate).getTime()
          )
        )
      );
  }

  // Update contract payment status based on latest payments
  private async updateContractPaymentStatus(contractId: string): Promise<void> {
    try {
      const payments = await firstValueFrom(
        this.getContractPayments(contractId)
      );
      const contract = await firstValueFrom(
        this.afs.doc<Contract>(`contracts/${contractId}`).valueChanges()
      );

      if (!contract) return;

      // Find the most recent payment
      const latestPayment = payments
        .filter((p) => p.status === 'Paid')
        .sort(
          (a, b) =>
            this.convertToDate(b.paymentDate).getTime() -
            this.convertToDate(a.paymentDate).getTime()
        )[0];

      // Find the next due payment
      const nextDuePayment = payments
        .filter(
          (p) =>
            p.status === 'Soon' ||
            p.status === 'Overdue' ||
            p.status === 'Upcoming'
        )
        .sort(
          (a, b) =>
            this.convertToDate(a.dueDate).getTime() -
            this.convertToDate(b.dueDate).getTime()
        )[0];

      const updates: Partial<Contract> = {
        updatedAt: new Date(),
      };

      if (latestPayment) {
        updates.paidAt = latestPayment.paymentDate;
      }

      if (nextDuePayment) {
        updates.nextPaymentAt = nextDuePayment.dueDate;
      }

      await this.afs.doc(`contracts/${contractId}`).update(updates);
    } catch (error) {
      console.error('Error updating contract payment status:', error);
    }
  }

  // Development/Testing Method - Generate sample payment records
  async generateSamplePayments(
    contractId: string,
    tenantId: string
  ): Promise<void> {
    const user = await firstValueFrom(this.afAuth.authState);
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get contract details to generate appropriate payments
    const contract = await firstValueFrom(
      this.afs.doc<Contract>(`contracts/${contractId}`).valueChanges()
    );

    if (
      !contract ||
      !contract.startDate ||
      !contract.paymentMethod?.billingCycle
    ) {
      throw new Error('Contract not found or missing payment details');
    }

    const startDate = this.convertToDate(contract.startDate);
    const today = new Date();
    const samplePayments: Omit<
      PaymentRecord,
      'id' | 'createdAt' | 'updatedAt' | 'createdBy'
    >[] = [];

    let currentDate = new Date(startDate);
    const billingCycle = contract.paymentMethod.billingCycle;
    const rentAmount = contract.rentAmount || 1000;
    const currency = contract.currency || 'USD';

    // Generate 6 months of payment history
    for (let i = 0; i < 6; i++) {
      const dueDate = new Date(currentDate);
      let period: string;

      // Generate period label
      if (billingCycle === 'Quarterly') {
        const quarter = Math.floor(dueDate.getMonth() / 3) + 1;
        period = `Q${quarter} ${dueDate.getFullYear()}`;
      } else if (billingCycle === 'Yearly') {
        period = `${dueDate.getFullYear()}`;
      } else {
        period = dueDate.toLocaleDateString('en-US', {
          month: 'long',
          year: 'numeric',
        });
      }

      // Determine payment status and date
      let status: 'Paid' | 'Overdue' | 'Soon' | 'Upcoming';
      let paymentDate: Date;
      let notes: string;

      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < -30) {
        // Old payments - mostly paid
        status = 'Paid';
        paymentDate = new Date(dueDate);
        paymentDate.setDate(
          paymentDate.getDate() + Math.floor(Math.random() * 5)
        ); // Paid within 5 days
        notes = 'Payment received on time';
      } else if (diffDays < 0) {
        // Recent overdue
        status = 'Overdue';
        paymentDate = dueDate;
        notes = 'Payment overdue';
      } else if (diffDays <= 7) {
        // Due soon
        status = 'Soon';
        paymentDate = dueDate;
        notes = 'Payment due soon';
      } else {
        // Future payments
        status = 'Upcoming';
        paymentDate = dueDate;
        notes = 'Future payment';
      }

      samplePayments.push({
        contractId,
        tenantId,
        amount: rentAmount,
        currency,
        paymentDate,
        dueDate,
        paymentMethod: contract.paymentMethod?.type || 'Bank Transfer',
        status,
        period,
        notes,
        transactionId: status === 'Paid' ? `TXN${Date.now()}${i}` : undefined,
      });

      // Move to next payment period
      if (billingCycle === 'Quarterly') {
        currentDate.setMonth(currentDate.getMonth() + 3);
      } else if (billingCycle === 'Yearly') {
        currentDate.setFullYear(currentDate.getFullYear() + 1);
      } else {
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    // Add sample payments to database
    const batch = this.afs.firestore.batch();

    for (const payment of samplePayments) {
      const docRef = this.afs.collection('payments').doc().ref;
      const paymentRecord: PaymentRecord = {
        ...payment,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: user.uid,
      };
      batch.set(docRef, paymentRecord);
    }

    await batch.commit();

    // Update contract payment status
    await this.updateContractPaymentStatus(contractId);

    console.log(
      `Generated ${samplePayments.length} sample payment records for contract ${contractId}`
    );
  }

  async sendClientInvitation(
    tenantId: string,
    registrationLinkMethod: string
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.afAuth.authState.subscribe(async (user) => {
        if (user) {
          try {
            // Get tenant data
            const tenantDoc = await this.afs
              .collection('tenants')
              .doc(tenantId)
              .get()
              .toPromise();
            if (!tenantDoc?.exists) {
              throw new Error('Tenant not found');
            }

            const tenantData = tenantDoc.data() as Tenant;
            const email = tenantData.basicInfo?.email;

            if (!email) {
              throw new Error('Tenant email not found');
            }

            // Fetch the user document to get ownerId and managerId
            const userDoc = await this.afs
              .collection('users')
              .doc(user.uid)
              .get()
              .toPromise();
            const userData = userDoc?.data() as any;
            const ownerId = userData?.adminId || userData?.ownerId || user.uid;
            const managerId = userData?.managerId || null;

            // Generate token for registration link
            const token = this.generateToken();
            const invitationId = this.afs.createId();

            // Create invitation record
            const invitationData = {
              id: invitationId,
              email: email,
              role: 'client',
              token: token,
              created_at: new Date(),
              status: 'pending',
              tenantId: tenantId,
              ownerId: ownerId,
              managerId: managerId,
              createdBy: user.uid,
              deliveryMethod: registrationLinkMethod,
            };

            // Save invitation to Firestore
            await this.afs
              .collection('invites')
              .doc(invitationId)
              .set(invitationData);

            // Generate registration link
            const registrationLink = `${window.location.origin}/create-account/${token}`;

            // Send invitation based on selected method
            if (registrationLinkMethod === 'Email') {
              // Use the email service to send the invitation
              const emailService = new EmailService();
              await emailService.sendInvitationEmail(
                email,
                registrationLink,
                'client'
              );
            } else if (registrationLinkMethod === 'WhatsApp') {
              // For WhatsApp, we'll just log the link for now
              // In a real implementation, you would integrate with WhatsApp API
              console.log(
                `WhatsApp invitation link for ${email}: ${registrationLink}`
              );
              // TODO: Implement actual WhatsApp integration
            }

            resolve();
          } catch (error) {
            console.error('Error sending client invitation:', error);
            reject(error);
          }
        } else {
          reject(new Error('User not authenticated'));
        }
      });
    });
  }

  // Add this helper method to generate tokens (similar to HR module)
  private generateToken(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }
}
