import { Component } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-register-employee',
  templateUrl: './register-employee.component.html',
  styleUrls: ['./register-employee.component.scss'],
})
export class RegisterEmployeeComponent {
  registerForm: FormGroup;

  validToken: boolean = false;
  invitation: any;

  constructor(
    private afAuth: AngularFireAuth,
    private afs: AngularFirestore,
    private router: Router,
    private activeRoute: ActivatedRoute
  ) {
    this.registerForm = new FormGroup({
      email: new FormControl(''),
      adminId: new FormControl(''),
      branchId: new FormControl(''),
      created_at: new FormControl(null),
      displayName: new FormControl('', Validators.required),
      managerId: new FormControl(''),
      role: new FormControl(''),
      id: new FormControl(''),
      status: new FormControl(true),
      password: new FormControl('', [
        Validators.required,
        Validators.minLength(6),
      ]),
      cpassword: new FormControl('', [
        Validators.required,
        Validators.minLength(6),
      ]),
    });
  }

  ngOnInit() {
    this.getInvitation();
  }

  getInvitation() {
    this.activeRoute.params.subscribe((data) => {
      console.log(data);
      this.afs
        .collection('invites', (ref) =>
          ref.where('token', '==', data['id']).where('status', '==', 'pending')
        )
        .valueChanges()
        .subscribe((res) => {
          console.log(res);
          if (res.length !== 0) {
            this.validToken = true;
            this.invitation = res[0];
            this.registerForm.controls['email'].setValue(this.invitation.email);
          }
        });
    });
  }

  register() {
    this.afAuth
      .createUserWithEmailAndPassword(
        this.registerForm.value.email,
        this.registerForm.value.password
      )
      .then((userCredential) => {
        // User registered successfully
        console.log('Registration successful', userCredential);

        const user = userCredential.user;
        if (user) {
          const userData: any = {
            email: this.registerForm.value.email,
            displayName: this.registerForm.value.displayName, // Assuming you have a displayName field in the form
            createdAt: new Date(),
            role: this.invitation.role,
            adminId: this.invitation.adminId,
            branchId: '',
            id: userCredential.user?.uid,
            status: true,
          };

          // Only add managerId if it exists
          if (this.invitation.managerId) {
            userData.managerId = this.invitation.managerId;
          }

          this.afs
            .collection('users')
            .doc(user.uid)
            .set(userData)
            .then(() => {
              console.log('User data saved to Firestore');

              this.afAuth
                .signInWithEmailAndPassword(
                  this.registerForm.value.email,
                  this.registerForm.value.password
                )
                .then((userCredential) => {
                  // User registered successfully
                  this.router.navigateByUrl('/hub');
                  console.log('Registration successful', userCredential);
                })
                .catch((error) => {
                  console.error('Registration error', error);
                });
            })
            .catch((error) => {
              console.error('Error saving user data to Firestore', error);
            });
        }
      })
      .catch((error) => {
        console.error('Registration error', error);
      });
  }
}
